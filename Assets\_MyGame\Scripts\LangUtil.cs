using System;
using System.Collections.Generic;
using UnityEngine;

public class LangUtil
{

    /// <summary>
    /// 获取语言包
    /// </summary>
    /// <param name="langId">语言表id</param>
    /// <param name="args"></param>
    /// <returns></returns>
    public static string GetText(string langId, params object[] args)
    {
        string msg = ConfigLangxx.GetData(langId);
        try
        {
            return string.Format(msg, args);
        }
        catch (Exception)
        {
            Debug.Log("msgArgs Is Error  " + langId);
            throw;
        }
    }
}