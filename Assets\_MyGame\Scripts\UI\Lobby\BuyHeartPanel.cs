using System;
using FairyGUI;

public class BuyHeartPanel : Panel
{
    public Action<int> OnBuySuccess;
    public BuyHeartPanel()
    {
        packName = "Lobby";
        compName = "BuyHeartPanel";
        modal = true;
    }

    private GTextField txtCount;
    private GTextField txtTime;

    private int heartGold;
    protected override void DoInitialize()
    {
        txtCount = contentPane.GetChild("txtCount").asTextField;
        txtTime = contentPane.GetChild("txtTime").asTextField;
        var btnBuy = contentPane.GetChild("btnBuy").asButton;
        var btnFree = contentPane.GetChild("btnFree").asButton;

        btnFree.icon = PathUtil.AutoShareOrVideoIcon;
        btnFree.text = "+" + ConfigSetting.freeHeartCount;
        var infoItem = ConfigItem.GetData(ItemId.ItemHeart);
        heartGold = infoItem.buyGold;
        btnBuy.text = heartGold.ToString();
        FlSdk.Inst.ReportVideoExposure(1);
        OnLoop(null);
        Timers.inst.Add(0.5f, -1, OnLoop);
    }

    private void OnLoop(object obj)
    {
        txtCount.text = HeartCooldown.Instance.GetCurrentHearts().ToString();
        txtTime.text = DateUtil.MinuteSecond(HeartCooldown.Instance.GetRemainingCooldownTime());
    }

    protected override void OnHide()
    {
        Timers.inst.Remove(OnLoop);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
            case "btnBuy":
                if (GameGlobal.ConsumeGold(heartGold))
                {
                    Hide();
                    FlSdk.Inst.ReportBuyStamina(1, false, HeartCooldown.Instance.GetCurrentHearts());
                    HeartCooldown.Instance.RecoverHeart(1);

                    OnBuySuccess?.Invoke(1);
                }
                break;
            case "btnFree":
                if (GameGlobal.IsUseShare)
                {
                    Platform.Instance.Share(ShareType.freeHeart, () =>
                    {
                        Hide();
                        FlSdk.Inst.ReportBuyStamina(ConfigSetting.freeHeartCount, false, HeartCooldown.Instance.GetCurrentHearts());
                        HeartCooldown.Instance.RecoverHeart(ConfigSetting.freeHeartCount);
                        GameGlobal.SwitchShareOrVideo();
                        OnBuySuccess?.Invoke(ConfigSetting.freeHeartCount);
                    });
                }
                else
                {
                    Platform.Instance.ShowVideoAd(AdType.freeHeart, () =>
                    {
                        Hide();
                        FlSdk.Inst.ReportBuyStamina(ConfigSetting.freeHeartCount, true, HeartCooldown.Instance.GetCurrentHearts());
                        HeartCooldown.Instance.RecoverHeart(ConfigSetting.freeHeartCount);
                        GameGlobal.SwitchShareOrVideo();
                        OnBuySuccess?.Invoke(ConfigSetting.freeHeartCount);
                    });
                }
                break;
        }
    }
}