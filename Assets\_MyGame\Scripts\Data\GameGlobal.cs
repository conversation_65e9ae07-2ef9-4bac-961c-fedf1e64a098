using UnityEngine;

public class GameGlobal
{
    public static float GameDurationTime = 10 * 60;
    public static float GameLeftTime = 0;
    public static bool IsRestart;

    public static int EnterLevel;

    public static int GamePlayedTime { get; set; }
    public static string OpenId;
    public static string ClientId;
    public static bool VibrationOn;
    public static bool IsNewPlayer;
    public static float VolumeBgm;
    public static float VolumeEffect;
    public static int CurHeartCount;
    private static int _gold;
    private static int _level;
    private static int _star;
    private static int _levelStar;
    private static int _comboCount;
    private static bool _needGuide;
    private static int _itemBulbCount;
    private static int _itemShuffleCount;
    private static int _itemTurnCount;
    private static int _itemMagnetCount;
    private static int _getFreeItemCount;
    private static int _pigGold;
    private static int _passLevel;
    private static int _passLevelIndex;
    private static string _channel;
    private static int _openSlotByShareCount;
    public static void Read()
    {
        IsNewPlayer = StorageMgr.IsNewPlayer;

        OpenId = StorageMgr.OpenId;
        ClientId = StorageMgr.ClientId;
        VibrationOn = StorageMgr.VibrationOn;
        VolumeBgm = StorageMgr.VolumeBgm;
        VolumeEffect = StorageMgr.VolumeEffect;

        _gold = StorageMgr.Gold;

        _passLevel = StorageMgr.PassLevel;
        _passLevelIndex = StorageMgr.PassLevelIndex;

        _star = StorageMgr.Star;
        _needGuide = StorageMgr.NeedGuide;
        _level = StorageMgr.Level;
        _pigGold = StorageMgr.PigGold;
        _itemBulbCount = StorageMgr.ItemBulbCount;
        _itemShuffleCount = StorageMgr.ItemShuffleCount;
        _itemTurnCount = StorageMgr.ItemTurnCount;
        _itemMagnetCount = StorageMgr.ItemMagnetCount;
        _getFreeItemCount = StorageMgr.GetFreeItemCount;
        _openSlotByShareCount = StorageMgr.OpenSlotByShareCount;
        //todo 合并写入/读取对象

        CurHeartCount = StorageMgr.GetHeart(HeartCooldown.Instance.MaxHearts);
        HeartCooldown.Instance.Init(CurHeartCount, StorageMgr.HeartCooldownSecondTicks);

#if UNITY_EDITOR
        _gold = 10000;
        // _channel = "C";
        // _level = 502;
        // GameGlobal.NeedGuide = true;
        // GameGlobal.IsNewPlayer = true;
        // IncrementPigGold(1000);
        // _passLevel = 5;
#endif
    }
    public static void Write()
    {
        //todo

    }

    public static string Channel { get => _channel; set { _channel = value; StorageMgr.Channel = _channel; } }

    public static void UnNewPlayer()
    {
        IsNewPlayer = StorageMgr.IsNewPlayer = false;
    }

    public static bool IsFirstTimePlayLevel(int level)
    {
        var playCount = StorageMgr.GetLevelPlayCount(level);
        return playCount == 1;
    }
    public static void IncrementLevelPlayCount(int level)
    {
        var playCount = StorageMgr.GetLevelPlayCount(level);
        playCount++;
        StorageMgr.SetLevelPlayCount(level, playCount);
    }

    public static int PassLevelIndex { get => _passLevelIndex; }
    public static void ResetPassLevelIndex()
    {
        _passLevelIndex = 0;
        StorageMgr.PassLevelIndex = _passLevelIndex;
    }
    public static void IncrementPassLevelIndex()
    {
        _passLevelIndex++;
        StorageMgr.PassLevelIndex = _passLevelIndex;
    }

    public static int PassLevel { get => _passLevel; }
    public static void ResetPassLevel()
    {
        _passLevel = 0;
        StorageMgr.PassLevel = _passLevel;
    }
    public static void IncrementPassLevel()
    {
        _passLevel++;
        StorageMgr.PassLevel = _passLevel;
    }

    public static int PigGold { get => _pigGold; }
    public static void ResetPigGold()
    {
        _pigGold = 0;
        StorageMgr.PigGold = _pigGold;
    }
    public static void IncrementPigGold(int value)
    {
        _pigGold += value;
        // if (_pigGold > ConfigSetting.pigMaxGold)
        // {
        //     _pigGold = ConfigSetting.pigMaxGold;
        // }
        StorageMgr.PigGold = _pigGold;
    }

    public static bool NeedGuide { get => _needGuide; }
    public static void GuideDone()
    {
        StorageMgr.NeedGuide = _needGuide = false;
    }

    public static int Level { get => _level; set { _level = value; StorageMgr.Level = _level; } }
    public static void IncrementLevel()
    {
        _level++;
        StorageMgr.Level = _level;
    }

    public static int Gold { get => _gold; }
    public static void IncrementGold(int count = 1)
    {
        _gold += count;
        StorageMgr.Gold = _gold;
        NotifyMgr.Event(NotifyNames.UpdateItemCount);
    }
    public static bool ConsumeGold(int count = 1)
    {
        if (_gold >= count)
        {
            _gold -= count;
            StorageMgr.Gold = _gold;
            NotifyMgr.Event(NotifyNames.UpdateItemCount);
            return true;
        }
        else
        {
            TipMgr.ShowTip(LangUtil.GetText("txtCoinNotEnough")); // 金币不足
        }
        return false;
    }

    public static int ItemBulbCount { get => _itemBulbCount; }
    public static int ItemShuffleCount { get => _itemShuffleCount; }
    public static int ItemTurnCount { get => _itemTurnCount; }
    public static int ItemMagnetCount { get => _itemMagnetCount; }

    public static void IncrementItemCount(int itemId, int count = 1)
    {
        //todo 频繁写入问题，找个时机写一次
        switch (itemId)
        {
            case ItemId.ItemGold:
                IncrementGold(count);
                break;
            case ItemId.ItemBulb:
                _itemBulbCount += count;
                StorageMgr.ItemBulbCount = _itemBulbCount;
                break;
            case ItemId.ItemShuffle:
                _itemShuffleCount += count;
                StorageMgr.ItemShuffleCount = _itemShuffleCount;
                break;
            case ItemId.ItemTurn:
                _itemTurnCount += count;
                StorageMgr.ItemTurnCount = _itemTurnCount;
                break;
            case ItemId.ItemMagnet:
                _itemMagnetCount += count;
                StorageMgr.ItemMagnetCount = _itemMagnetCount;
                break;
        }
    }

    public static bool UseBulb(bool add2UsedCount)
    {
        if (_itemBulbCount > 0)
        {
            _itemBulbCount--;
            StorageMgr.ItemBulbCount = _itemBulbCount;
            return true;
        }
        return false;
    }
    public static bool UseShuffle(bool add2UsedCount)
    {
        if (_itemShuffleCount > 0)
        {
            _itemShuffleCount--;
            StorageMgr.ItemShuffleCount = _itemShuffleCount;
            return true;
        }
        return false;
    }
    public static bool UseTurn(bool add2UsedCount)
    {
        if (_itemTurnCount > 0)
        {
            _itemTurnCount--;
            StorageMgr.ItemTurnCount = _itemTurnCount;
            return true;
        }
        return false;
    }
    public static bool HasMagnet { get => _itemMagnetCount > 0; }
    public static bool UseMagnet(bool add2UsedCount)
    {
        if (_itemMagnetCount > 0)
        {
            _itemMagnetCount--;
            StorageMgr.ItemMagnetCount = _itemMagnetCount;
            return true;
        }
        return false;
    }

    public static int Star { get => _star; set { _star = value; StorageMgr.Star = _star; } }

    public static int LevelStar { get { return _levelStar; } }
    public static void ResetLevelStar() { _levelStar = 0; }
    public static void IncrementLevelStar(int starCount) { _levelStar += starCount; }

    public static int ComboCount { get { return _comboCount; } }
    public static void ResetComboCount() { _comboCount = 0; }
    public static void IncrementComboCount(int count) { _comboCount += count; }
    public static int GetFreeItemCount
    {
        get { return _getFreeItemCount; }
        set
        {
            _getFreeItemCount = value;
            StorageMgr.GetFreeItemCount = _getFreeItemCount;
        }
    }
    public static void SwitchShareOrVideo()
    {
        _getFreeItemCount++;
        StorageMgr.GetFreeItemCount = _getFreeItemCount;
    }
    public static bool IsUseShare
    {
        get
        {
            return _getFreeItemCount == 0 || _getFreeItemCount % 6 == 0;
        }
    }

    public static void SwitchOpenSlotShareOrVideo()
    {
        _openSlotByShareCount++;
        StorageMgr.OpenSlotByShareCount = _openSlotByShareCount;
    }
    public static bool OpenSlotIsUseShare
    {
        get
        {
            return false;
            // return _openSlotByShareCount % 6 == 0;
        }
    }
}