using FairyGUI;

public class ResultFailPanel : Panel
{
    public ResultFailPanel()
    {
        packName = "Battle";
        compName = "ResultFailPanel";
        modal = true;
    }

    private FailReason failReason;
    private GButton btnFree;
    private GButton btnFreeGet;
    private Controller c1;
    protected override void DoInitialize()
    {
        btnFree = contentPane.GetChild("btnFree").asButton;
        btnFreeGet = contentPane.GetChild("btnFreeGet").asButton;
        c1 = contentPane.GetController("c1");
    }

    public void SetData(FailReason failReason)
    {
        this.failReason = failReason;

        c1.selectedIndex = failReason == FailReason.SlotFull ? 0 : 1;

        var lblGold = contentPane.GetChild("lblGold").asTextField;
        lblGold.text = GameGlobal.LevelStar.ToString();

        btnFree.icon = GameGlobal.IsUseShare ? PathUtil.IconShare : PathUtil.IconVideo;
        btnFreeGet.icon = GameGlobal.IsUseShare ? PathUtil.IconShare : PathUtil.IconVideo;
        if (!GameGlobal.IsUseShare)
        {
            FlSdk.Inst.ReportVideoExposure(1);
        }

        contentPane.touchable = false;
        Timers.inst.Add(0.8f, 1, (obj) =>
        {
            if (contentPane != null && !contentPane.isDisposed)
            {
                contentPane.touchable = true;
            }
        });
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                GameRoot.SwitchHandler<LobbyHandler>();
                FlSdk.Inst.ReportChapterEnd(false);
                break;
            case "btnFree":
            case "btnFreeGet":
                ContinueByFree();
                break;
            // case "btnGold":
            //     ContinueWithGold();
            //     break;
            case "btnRestart":
                var success = new CmdEnterBattle().Execute(0, false, true);
                if (success)
                {
                    Hide();
                }
                break;
        }
    }

    private void ContinueByFree()
    {
        string positionTag = failReason == FailReason.TimeOut ? ShareType.reliveTimeout : ShareType.relive;
        if (GameGlobal.IsUseShare)
        {
            Platform.Instance.Share(positionTag, () =>
            {
                Continue();
                GameGlobal.SwitchShareOrVideo();
            });
        }
        else
        {
            Platform.Instance.ShowVideoAd(positionTag, () =>
            {
                Continue();
                GameGlobal.SwitchShareOrVideo();
            });
        }
    }

    private void ContinueWithGold()
    {
        // var needGold = failReason == FailReason.TimeOut ? ConfigSetting.addTimeNeedGold : ConfigSetting.clearSlotBarNeedGold;
        // if (GameGlobal.ConsumeGold(needGold))
        // {
        //     Continue();
        // }
    }

    private void Continue()
    {
        Hide();
        // if (failReason == FailReason.TimeOut)
        // {
        //     NotifyMgr.Event(NotifyNames.AddGameTime, ConfigSetting.addTimeSecond);
        // }
        // else if (failReason == FailReason.SlotFull)
        // {
        //     BattleScene.Inst.ClearSlotBar();
        //     NotifyMgr.Event(NotifyNames.AddGameTime, 30);
        // }
        NotifyMgr.Event(NotifyNames.ContinueGame);
    }
}