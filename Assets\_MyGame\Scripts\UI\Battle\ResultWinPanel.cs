using System.Text;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

public class ResultWinPanel : Panel
{
    public ResultWinPanel()
    {
        packName = "Battle";
        compName = "ResultWinPanel";
        modal = true;
    }

    private StringBuilder tempStr = new();
    // private MediatorMultReward multBarMdr;
    private GButton btnGet;
    private GTextField txtStarCount;
    private GTextField txtGoldCount;
    private GButton btnNext;
    private GComponent coinBar;
    private GTextField txtGold;
    private Vector2 coinFlyTargetPos;

    protected override void DoInitialize()
    {
        txtStarCount = contentPane.GetChild("txtStarCount").asTextField;
        txtGoldCount = contentPane.GetChild("txtGoldCount").asTextField;

        btnGet = contentPane.GetChild("btnGet").asButton;
        btnNext = contentPane.GetChild("btnNext").asButton;
        coinBar = contentPane.GetChild("coinBar").asCom;
        var coinIcon = coinBar.GetChild("icon");
        coinFlyTargetPos = coinIcon.LocalToRoot(Vector2.zero, GRoot.inst);

        // coinFlyTargetPos = coinIcon.LocalToRoot(coinIcon.xy, GRoot.inst);
        txtGold = coinBar.GetChild("txtNum").asTextField;
        FlSdk.Inst.ReportVideoExposure(1);
        // multBarMdr = new MediatorMultReward(contentPane.GetChild("multBar"), ConfigSetting.Setting.goldMults);
        var lblPassPercent = contentPane.GetChild("lblPassPercent").asTextField;
        var infoGate = ConfigGate.GetData(GameGlobal.Level);
        lblPassPercent.text = LangUtil.GetText("txtHardTipsPass", infoGate.percentDesc); // 您已击败全国[size=46]{0}[/size]的麻友!
        SetData();
    }

    public void SetData()
    {
        if (GameGlobal.LevelStar == 0)
        {
            //保底  todo 暂定数值
            GameGlobal.IncrementLevelStar(50);
        }


        txtStarCount.text = GameGlobal.LevelStar.ToString();
        txtGoldCount.text = "0";

        var startStar = GameGlobal.LevelStar;
        DOTween.To(
            () => startStar,
            star =>
            {
                if (!txtStarCount.isDisposed)
                {
                    txtStarCount.text = star.ToString();
                }
            },
            0,
            1f
        ).SetDelay(1);

        var startGold = 0;
        DOTween.To(
            () => startGold,
            gold =>
            {
                if (!txtGoldCount.isDisposed)
                {
                    txtGoldCount.text = gold.ToString();
                }
            },
            GameGlobal.LevelStar,
            1f
        ).SetDelay(1);

        // btnNext.text = GameGlobal.LevelStar.ToString();

        // multBarMdr.MoveArrow();
        // Timers.inst.AddUpdate(OnUpdate);
        // OnUpdate(null);


        // GameGlobal.IncrementPigGold(ConfigSetting2.pigAddGold);
    }

    // private void OnUpdate(object param)
    // {
    //     tempStr.Clear();
    //     if (GameGlobal.LevelStar == 0)
    //         return;
    //     // var multValue = multBarMdr.MultValue;
    //     // tempStr.Append("+");
    //     // tempStr.Append(Mathf.FloorToInt(GameGlobal.LevelStar * multValue));
    //     tempStr.Append(Mathf.FloorToInt(GameGlobal.LevelStar * 2));
    //     // btnGet.text = tempStr.ToString();
    // }

    protected override void OnHide()
    {
        // Timers.inst.Remove(OnUpdate);
        // multBarMdr.Remove();
    }

    protected override void OnMouseClick(string targetName)
    {
        if (isAlreadyGet)
            return;

        switch (targetName)
        {
            case "btnNext":
                isAlreadyGet = true;
                FlyGold(btnNext.xy, 10);
                break;
            case "btnGet":
                // multBarMdr.Stop();
                Platform.Instance.ShowVideoAd(AdType.winGetMultGold, () =>
                {
                    isAlreadyGet = true;
                    GetMultStar();
                }
                // () => { multBarMdr.MoveArrow(); }, (code, msg) => { multBarMdr.MoveArrow(); }
                );
                break;
        }
    }

    private bool isAlreadyGet = false;
    private void GetMultStar()
    {
        var curStar = GameGlobal.LevelStar;
        // var multValue = multBarMdr.MultValue;
        var multValue = 3;
        var mulStarCount = Mathf.FloorToInt(curStar * (multValue - 1));
        GameGlobal.IncrementLevelStar(mulStarCount);
        var endStar = GameGlobal.LevelStar;
        DOTween.To(
            () => curStar,
            star => { txtGoldCount.text = star.ToString(); }, endStar, 1)
            .SetDelay(0.2f)
            .OnComplete(() =>
            {
                txtGoldCount.scale = Vector2.one * 0.8f;
                txtGoldCount.TweenScale(Vector2.one, 0.5f).SetEase(EaseType.BackOut);
            });

        FlyGold(btnGet.xy, 20);
    }

    private void FlyGold(Vector2 startPos, int maxCount)
    {
        coinBar.visible = true;
        coinBar.alpha = 0;
        coinBar.SetScale(0, 0);
        coinBar.TweenFade(1, 0.2f);
        coinBar.TweenScale(Vector2.one, 0.2f).SetEase(EaseType.BackOut);

        var flyCont = Mathf.Min(GameGlobal.LevelStar, maxCount);
        txtGold.text = GameGlobal.Gold.ToString();

        //保存数据
        GameGlobal.IncrementGold(GameGlobal.LevelStar);
        GameGlobal.ResetLevelStar();

        var targetPos = coinFlyTargetPos;
        for (int i = 0; i < flyCont; i++)
        {
            var coin = UIPackage.CreateObject(packName, "Coin");
            var bornPos = startPos + Random.insideUnitCircle * Random.Range(-100, 100);
            coin.xy = startPos;
            contentPane.AddChild(coin);
            coin.TweenMove(bornPos, 0.2f).OnComplete(() =>
            {
                coin.TweenMove(targetPos, 0.5f).SetDelay(Random.Range(0, 0.2f)).SetEase(EaseType.BackIn).OnComplete(() =>
                {
                    coin.Dispose();
                });
            });
        }

        DOVirtual.DelayedCall(0.8f, () =>
        {
            txtGold.text = GameGlobal.Gold.ToString();
        });
        DOVirtual.DelayedCall(1.5f, () =>
        {
            Hide();
        });
    }
}