using System;
using System.Collections.Generic;
using LitJson;
using UnityEngine;

[Serializable]
public class TableSO : ScriptableObject
{
    public InfoBoxLevel[] boxLevels;
    public InfoGate[] gates;
    public InfoGate[] gatesB;
    public InfoGate[] gatesC;
    public InfoItem[] items;
    public InfoMajiang[] majiangs;
    public InfoSetting[] settings;
    public InfoShopItem[] shopItems;
    public InfoLang[] languis;
    public ConfigInfoBase[] GetDatas(string tableName)
    {
        return tableName switch
        {
            "BoxLevel" => boxLevels,
            "Gate" => gates,
            "GateB" => gatesB,
            "GateC" => gatesC,
            "Item" => items,
            "Majiang" => majiangs,
            "Setting" => settings,
            "ShopItem" => shopItems,
            "Langui" => languis,
            _ => null,
        };
    }

    //以下逻辑只在编辑器中有效
#if UNITY_EDITOR
    public static string[] tableNames = new string[] {
        "BoxLevel",
        "Gate",
        "GateB",
        "GateC",
        "Item",
        "Majiang",
        "Setting",
        "ShopItem",
        "Langui",
    };

    public void GenerateSO(string tableName, JsonData json)
    {
        switch (tableName)
        {
            case "BoxLevel":
                boxLevels = CreateArray<InfoBoxLevel>(json);
                break;
            case "Gate":
                gates = CreateArray<InfoGate>(json);
                break;
            case "GateB":
                gatesB = CreateArray<InfoGate>(json);
                break;
            case "GateC":
                gatesC = CreateArray<InfoGate>(json);
                break;
            case "Item":
                items = CreateArray<InfoItem>(json);
                break;
            case "Majiang":
                majiangs = CreateArray<InfoMajiang>(json);
                break;
            case "Setting":
                settings = CreateArray<InfoSetting>(json);
                break;
            case "ShopItem":
                shopItems = CreateArray<InfoShopItem>(json);
                break;
            case "Langui":
                languis = CreateArray<InfoLang>(json);
                break;
        }
    }

    public static T[] CreateArray<T>(JsonData json) where T : ConfigInfoBase, new()
    {
        var list = new List<T>();
        if (json.IsArray)
        {
            for (int i = 0; i < json.Count; i++)
            {
                T data = new T();
                data.Parse(json[i]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        else
        {
            string[] keys = new string[json.Count];
            json.Keys.CopyTo(keys, 0);
            for (int i = 0; i < keys.Length; i++)
            {
                string key = keys[i];
                T data = new T();
                data.Parse(json[key]);
                data.uniqueKey = data.GetKey(i).ToString();
                list.Add(data);
            }
        }
        var result = list.ToArray();
        return result;
    }
#endif
}