using System;
using FairyGUI;
using UnityEngine;
public class MediatorComboBar : MediatorBase
{
    private GComponent contentPane;
    private GTextField txtMult;
    private GComponent comboTip;

    public float totalTime = 7;
    private float leftTime;
    private float initBarWidth;
    private GProgressBar progressBar;
    private GTextField lblCombo;
    private GImage tipBg;
    private float comboTipEndX;
    public MediatorComboBar(GObject gObj, GComponent comboTip) : base(gObj)
    {
        contentPane = gObj.asCom;
        progressBar = gObj.asProgress;
        txtMult = contentPane.GetChild("txtMult").asTextField;
        txtMult.text = "x1";
        progressBar.value = 0;

        this.comboTip = comboTip;
        tipBg = comboTip.GetChild("tipBg").asImage;
        comboTipEndX = comboTip.x;
        lblCombo = comboTip.GetChild("lblCombo").asTextField;
        NotifyMgr.On(NotifyNames.MergeDone, this, OnMergeDone);
    }

    public override void Remove()
    {
        TimerRemove(OnLoop);
        NotifyMgr.OffAllCaller(this);
        base.Remove();
    }

    private void OnMergeDone()
    {
        ShowComboTip();
        UpdateMult();
        leftTime = totalTime;
        OnLoop(null);
        TimerLoop(0, -1, OnLoop);
    }

    private void ShowComboTip()
    {
        GTween.Kill(comboTip);
        GTween.Kill(tipBg);

        lblCombo.text = GameGlobal.ComboCount.ToString();

        tipBg.SetScale(1.3f, 1.3f);
        tipBg.TweenScale(Vector2.one, 0.2f).SetDelay(0.2f);

        comboTip.alpha = 1;
        comboTip.SetScale(0, 0);
        comboTip.TweenScale(Vector2.one, 0.2f).SetEase(EaseType.BackOut);
        comboTip.x = comboTipEndX + comboTip.width * 0.5f;
        comboTip.TweenMoveX(comboTipEndX, 0.2f).SetEase(EaseType.BackOut);
        comboTip.visible = true;
        comboTip.TweenFade(0, 0.3f).SetDelay(1f);
    }

    private void UpdateMult()
    {
        var comboCount = GameGlobal.ComboCount > ConfigSetting.maxComboCount ? ConfigSetting.maxComboCount : GameGlobal.ComboCount;
        txtMult.text = "x" + comboCount;
        txtMult.TweenScale(Vector2.one * 1.2f, 0.1f).OnComplete(() =>
        {
            if (txtMult != null && !txtMult.isDisposed)
            {
                txtMult.TweenScale(Vector2.one, 0.1f);
            }
        });
    }

    private void OnLoop(object param)
    {
        if (contentPane.isDisposed)
            return;

        leftTime -= Time.deltaTime;
        if (leftTime <= 0)
        {
            leftTime = 0;
            GameGlobal.ResetComboCount();
            txtMult.text = "x1";
            TimerRemove(OnLoop);
        }
        progressBar.value = leftTime / totalTime * 100;
    }

    internal void Stop()
    {
        TimerRemove(OnLoop);
    }

    internal void Continue()
    {
        TimerLoop(0, -1, OnLoop);
    }
}