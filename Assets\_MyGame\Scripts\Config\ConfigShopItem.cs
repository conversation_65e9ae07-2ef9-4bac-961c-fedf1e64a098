using System.Collections.Generic;

public class ConfigShopItem : ConfigBase
{
    private static InfoShopItem emptyItem = new InfoShopItem()
    {
        id = -1,
    };

    public static List<InfoShopItem> GetDatas()
    {
        return ConfigManager.GetConfig<ConfigShopItem>().GetList<InfoShopItem>();
    }

    public static InfoShopItem GetData(int id)
    {
        var info = ConfigManager.GetConfig<ConfigShopItem>().GetData<InfoShopItem>(id);
        if (info == null)
        {
            info = emptyItem;
        }
        return info;
    }
}