using System;
using FairyGUI;

public class PigPanel : Panel
{
    public Action<int> OnGetSuccess;
    public PigPanel()
    {
        packName = "Lobby";
        compName = "PigPanel";
        modal = true;
    }

    private GButton btnGet;
    private GButton btnGo;
    private GProgressBar progressBar;
    protected override void DoInitialize()
    {
        btnGet = contentPane.GetChild("btnGet").asButton;
        // btnGo = contentPane.GetChild("btnGo").asButton;
        progressBar = contentPane.GetChild("progressBar").asProgress;

        var txtAddGold = contentPane.GetChild("txtAddGold").asTextField;
        var txtGetGold = contentPane.GetChild("txtGetGold").asTextField;
        var txtMaxGold = contentPane.GetChild("txtMaxGold").asTextField;
        var txtDesc1 = contentPane.GetChild("txtDesc1").asTextField;
        var txtDesc2 = contentPane.GetChild("txtDesc2").asTextField;

        txtAddGold.text = ConfigSetting.pigAddGold.ToString();
        txtGetGold.text = ConfigSetting.pigGetGold.ToString();
        txtMaxGold.text = ConfigSetting.pigMaxGold.ToString();

        txtDesc1.text = LangUtil.GetText("txtPigBankDesc1", ConfigSetting.pigGetGold, ConfigSetting.pigMaxGold); // 满{0}金币可以领取\n最多可存储{1}金币
        txtDesc2.text = LangUtil.GetText("txtPigBankDesc2", ConfigSetting.pigAddGold);// 每次通关可存入{0}金币

        progressBar.max = ConfigSetting.pigMaxGold;
        progressBar.value = GameGlobal.PigGold;
        if (GameGlobal.PigGold < ConfigSetting.pigGetGold)
        {
            // btnGo.visible = true;
            btnGet.enabled = false;
        }
        else
        {
            // btnGo.visible = false;
            btnGet.enabled = true;
        }

        FlSdk.Inst.ReportVideoExposure(1);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
            case "btnGet":
                Platform.Instance.ShowVideoAd(AdType.pig, () =>
                {
                    Hide();
                    var gold = GameGlobal.PigGold;
                    GameGlobal.IncrementGold(gold);
                    GameGlobal.ResetPigGold();
                    OnGetSuccess?.Invoke(gold);
                });
                break;
            case "btnGo":
                Hide();
                new CmdEnterBattle().Execute();
                break;
        }
    }
}