using System.Text;
using DG.Tweening;
using FairyGUI;
using UnityEngine;

class BattlePanel : Panel
{
    public BattlePanel()
    {
        packName = "Battle";
        compName = "BattlePanel";
    }
    private GTextInput txtNum;
    private GTextInput txtDelay;
    private MediatorLeftTimeBar leftTimeBar;
    private bool isGameWin;
    private MediatorComboBar comboBar;
    private ItemButton btnBulb;
    private ItemButton btnShuffle;
    private ItemButton btnTurn;
    // private ItemButton btnMagnet;
    private GLoader3D itemEffect;
    private bool isGameDone;
    private GTextField txtInfo;
    private StringBuilder sb = new StringBuilder();
    private int canUseItemMax;
    private GComponent tipItem;
    private GObject mask;
    private GButton btnGm;

    protected override void DoInitialize()
    {
        isGameWin = false;
        itemEffect = contentPane.GetChild("itemEffect").asLoader3D;
        mask = contentPane.GetChild("mask");
        canUseItemMax = 1;
        btnBulb = new ItemButton(ItemId.ItemBulb, canUseItemMax, contentPane.GetChild("btnBulb"));
        btnShuffle = new ItemButton(ItemId.ItemShuffle, canUseItemMax, contentPane.GetChild("btnShuffle"));
        btnTurn = new ItemButton(ItemId.ItemTurn, canUseItemMax, contentPane.GetChild("btnTurn"));
        // btnMagnet = new ItemButton(ItemId.ItemMagnet, canUseItemMax, contentPane.GetChild("btnMagnet"));

        if (GameGlobal.NeedGuide)
        {
            var guideTip = contentPane.GetChild("guideTip").asCom;
            guideTip.visible = true;
        }
        var txtVersion = contentPane.GetChild("txtVersion").asTextField;
        txtVersion.text = $"ver {GameConfig.GetVer()}.{AssetBundleManager.GetVersion2()}";

        var txtLevel = contentPane.GetChild("txtLevel").asTextField;
        txtInfo = contentPane.GetChild("txtInfo").asTextField;
        txtLevel.text = $"{LangUtil.GetText("txtLevelPrefix")}  {GameGlobal.Level}"; // 关卡 
        var txtChannel = contentPane.GetChild("txtChannel").asTextField;
        txtChannel.text = GameGlobal.Channel;

        btnGm = contentPane.GetChild("btnGm").asButton;
#if UNITY_EDITOR
        btnGm.visible = true;
#endif

        leftTimeBar = AddMediator(new MediatorLeftTimeBar(contentPane.GetChild("leftTimeBar").asCom)) as MediatorLeftTimeBar;
        tipItem = contentPane.GetChild("tipItem").asCom;
        var comboBarCom = contentPane.GetChild("comboBar").asCom;
        var comboTipCom = contentPane.GetChild("comboTip").asCom;
        comboBar = AddMediator(new MediatorComboBar(comboBarCom, comboTipCom)) as MediatorComboBar;
        AddMediator(new MediatorStarBar(contentPane.GetChild("starBar").asCom));
        NotifyMgr.On(NotifyNames.UpdateItemCount, this, OnUpdateItemCount);
        NotifyMgr.On(NotifyNames.AddGameTime, this, OnAddGameTime);
        NotifyMgr.On(NotifyNames.ContinueGame, this, OnContinueGame);
        NotifyMgr.On(NotifyNames.UpdateMajiangCount, this, OnUpdateMajiangCount);
        NotifyMgr.On(NotifyNames.SelectItemMajiang, this, OnSelectItemMajiang);
        NotifyMgr.On(NotifyNames.ResumeGame, this, OnResumeGame);
#if UNITY_EDITOR
        txtNum = contentPane.GetChild("txtNum").asTextInput;
        txtDelay = contentPane.GetChild("txtDelay").asTextInput;
        txtNum.text = StorageMgr.EditorNums;
        txtDelay.text = StorageMgr.EditorDelay;
#endif

        var infoGate = ConfigGate.GetData(GameGlobal.Level);
        if (infoGate != null && infoGate.isHard)
        {
            Create((HardTipsPanel panel) =>
            {
                panel.SetData(infoGate);
            });
        }

    }

    private void OnAddGameTime(object data)
    {
        int addTime = (int)data;
        leftTimeBar.AddTime(addTime);
    }

    private void OnUpdateMajiangCount()
    {
        var typeCount = BattleScene.Inst.GetMajinagTypeCount();
        var count = BattleScene.Inst.GetMajinagWithItemCount();
        sb.Clear();
        sb.Append(LangUtil.GetText("txtMajiangStats", typeCount, count)); // 种类 {0}   总数 {1}
        txtInfo.text = sb.ToString();
    }

    private void OnUpdateItemCount()
    {
        UpdateItemCount();
    }
    private void OnContinueGame()
    {
        GameStart(battleHandler, true);
        Resume();
    }
    public void Pause()
    {
        comboBar?.Stop();
        leftTimeBar?.Stop();
    }
    public void Resume()
    {
        comboBar?.Continue();
        leftTimeBar?.Continue();
    }

    private BattleHandler battleHandler;
    public void GameStart(BattleHandler battleHandler, bool failAndContinue = false)
    {
        this.battleHandler = battleHandler;
        isGameDone = false;
        InitItem(failAndContinue);
    }

    public void GameWin()
    {
        FlSdk.Inst.ReportChapterEnd(true);
        isGameDone = true;
        Pause();

        Create<ResultWinPanel>((panel) =>
        {
            panel.OnClosed = (type) =>
            {
                // var curLevel = GameGlobal.Level;
                // if (curLevel > 2)
                // {
                //     var preLevel = GameGlobal.Level - 1;
                //     var infoGate = ConfigGate.GetData(curLevel);
                //     var preInfoGate = ConfigGate.GetData(preLevel);
                //     if (infoGate.majiangTypeCount > preInfoGate.majiangTypeCount)
                //     {
                //         var startNum = preInfoGate.majiangTypeCount + 1;
                //         var typeCount = infoGate.majiangTypeCount - preInfoGate.majiangTypeCount;
                //         Create((CollectPanel collectPanel) =>
                //         {
                //             BattleScene.Inst.ChangeLightRot(true);
                //             collectPanel.SetData(startNum, typeCount);
                //             collectPanel.OnClosed = (type) =>
                //             {
                //                 BattleScene.Inst.ChangeLightRot(false);
                //                 new CmdEnterBattle().Execute(true);
                //             };
                //         });
                //     }
                //     else
                //     {
                //         new CmdEnterBattle().Execute(true);
                //     }
                // }
                // else
                // {
                new CmdEnterBattle().Execute(isFree: true);
                // }
            };
        });
    }

    public void GameOver()
    {
        // FlSdk.Inst.ReportChapterEnd(false, leftTimeBar.GetPlayTime());
        isGameDone = true;
        Pause();
        new CmdShowResult().Execute(FailReason.SlotFull, true);
    }

    private void InitItem(bool failAndContinue)
    {
        UpdateItemCount();

        if (failAndContinue)
            return;
        FlSdk.Inst.ResetTempData();

        var level = GameGlobal.Level;
        if (level == 1)
        {

        }
        // else if (level == 3)
        // {
        //     GiveFreeItem(level, ItemId.ItemBulb, ConfigSetting.guideItemCount);
        // }
        // else if (level == 4)
        // {
        //     GiveFreeItem(level, ItemId.ItemShuffle, ConfigSetting.guideItemCount);
        // }
        // else if (level == 5)
        // {
        //     GiveFreeItem(level, ItemId.ItemTurn, ConfigSetting.guideItemCount);
        // }
        else if (level == 6)
        {
            // GiveFreeItem(level, ItemId.ItemMagnet, ConfigSetting.Setting.guideItemCount);
        }
        else
        {
            // if (!failAndContinue)
            // {
            //     int idx = Random.Range(1, 101);
            //     int itemId;
            //     if (idx <= 40)
            //     {
            //         itemId = ItemId.ItemBulb;
            //     }
            //     else if (idx <= 50)
            //     {
            //         itemId = ItemId.ItemShuffle;
            //     }
            //     else if (idx <= 70)
            //     {
            //         itemId = ItemId.ItemTurn;
            //     }
            //     else
            //     {
            //         itemId = ItemId.ItemMagnet;
            //     }
            //     GiveFreeItem(level, itemId, 1);
            // }
        }
    }
    private void GiveFreeItem(int level, int itemId, int count)
    {
        if (GameGlobal.IsFirstTimePlayLevel(level))
        {
            mask.visible = true;
            var freeCount = count;
            GameGlobal.IncrementItemCount(itemId, freeCount);

            ItemButton btnItem = null;
            if (itemId == ItemId.ItemBulb) btnItem = btnBulb;
            else if (itemId == ItemId.ItemShuffle) btnItem = btnShuffle;
            else if (itemId == ItemId.ItemTurn) btnItem = btnTurn;
            // else btnItem = btnMagnet;

            if (btnItem == null)
                return;
            for (int i = 0; i < freeCount; i++)
            {
                var startPos = new Vector2(GRoot.inst.width * 0.5f, GRoot.inst.height * 0.5f);
                if (freeCount > 1)
                {
                    startPos.x += Random.Range(-100, 100);
                    startPos.y += Random.Range(-100, 100);
                }
                UIEffectUtil.FlyItemTo(contentPane, startPos, btnItem.GetPos(), itemId, 1, i * 0.1f + 2f);
            }
            DOVirtual.DelayedCall(2.5f, () =>
            {
                if (contentPane.isDisposed)
                    return;
                UpdateItemCount();
                ShowItemTip(itemId);
                mask.visible = false;
            });
        }
    }

    private void UpdateItemCount()
    {
        btnBulb.SetItemCount(GameGlobal.ItemBulbCount);
        btnShuffle.SetItemCount(GameGlobal.ItemShuffleCount);
        btnTurn.SetItemCount(GameGlobal.ItemTurnCount);
        // btnMagnet.SetItemCount(GameGlobal.ItemMagnetCount);
    }

    private int[] itemIds = new int[] { ItemId.ItemBulb, ItemId.ItemShuffle, ItemId.ItemTurn };
    private void ShowRandomItemTip(float delayShow)
    {
        if (contentPane.isDisposed)
            return;

        DOVirtual.DelayedCall(delayShow, () =>
        {
            var itemId = itemIds[Random.Range(0, itemIds.Length)];
            ShowItemTip(itemId);
            ShowRandomItemTip(Random.Range(30, 60));
        });
    }


    private void ShowItemTip(int itemId)
    {
        var infoItem = ConfigItem.GetData(itemId);
        if (infoItem == null)
            return;
        tipItem.touchable = false;
        var lblItemName = tipItem.GetChild("lblItemName").asTextField;
        var lblItemDesc = tipItem.GetChild("lblItemDesc").asTextField;
        lblItemName.text = infoItem.name;
        lblItemDesc.text = infoItem.desc;
        Vector2 tipPos = Vector2.zero;
        switch (itemId)
        {
            case ItemId.ItemBulb:
                tipPos = btnBulb.GetPos();
                tipPos.x += 20;
                break;
            case ItemId.ItemShuffle:
                tipPos = btnShuffle.GetPos();
                break;
            case ItemId.ItemTurn:
                tipPos = btnTurn.GetPos();
                break;
                // case ItemId.ItemMagnet:
                //     tipPos = btnMagnet.GetPos();
                //     tipPos.x -= 20;
                //     break;
        }
        tipPos.y -= 70;
        tipItem.xy = tipPos;

        tipItem.visible = true;
        DOVirtual.DelayedCall(5, () =>
        {
            if (tipItem.isDisposed)
                return;
            tipItem.visible = false;
        });
    }

    protected override void OnMouseClick(string targetName)
    {
        if (isGameDone)
            return;

        switch (targetName)
        {
            case "btnSetting":
                Create((SettingPanel panel) =>
                {
                    panel.SetData(SettingPanel.SettingViewMode.Pause);
                    Pause();
                    panel.OnClosed = (type) =>
                    {
                        if (contentPane.isDisposed)
                            return;

                        if (type == 0)
                        {
                            OnResumeGame();
                        }
                        else if (type == SettingPanel.CloseType_Restart)
                        {
                            FlSdk.Inst.ReportChapterEnd(false);
                        }
                    };
                });
                break;
            case "btnBulb":
                UseItem(ItemId.ItemBulb);
                break;
            case "btnShuffle":
                UseItem(ItemId.ItemShuffle);
                break;
            case "btnTurn":
                UseItem(ItemId.ItemTurn);
                break;
            // case "btnMagnet":
            //     UseItem(ItemId.ItemMagnet);
            //     break;

#if UNITY_EDITOR
            case "btnCreate":
                StorageMgr.EditorNums = txtNum.text;
                StorageMgr.EditorDelay = txtDelay.text;
                var dealy = string.IsNullOrEmpty(txtDelay.text) ? 0 : float.Parse(txtDelay.text);
                var numAry = txtNum.text.Split(",");
                var nums = new int[numAry.Length];
                for (int i = 0; i < nums.Length; i++)
                {
                    nums[i] = int.Parse(numAry[i]);
                }
                BattleScene.Inst.Test(nums, dealy);
#endif
                break;
            case "btnGm":
                ShowGmTool();
                break;
        }
    }

    private GmTool gmTool;
    private void ShowGmTool()
    {
        if (gmTool == null)
        {
            gmTool = new GmTool();
            gmTool.OnPassGate = OnGmPassGate;
        }

        if (!gmTool.IsShow)
        {
            gmTool.Show(contentPane, btnGm.TransformPoint(new Vector2(0, 80), contentPane));
        }
        else
        {
            gmTool.Hide();
        }
    }

    private void OnGmPassGate()
    {
        battleHandler?.OnGameDone(true);
    }

    private void OnResumeGame()
    {
        Resume();
    }

    private void UseItem(int itemId, bool isByVideo = false, bool add2UsedCount = true)
    {
        var canUse = false;
        Vector2 flyPos = Vector2.zero;
        switch (itemId)
        {
            case ItemId.ItemBulb:
                if (GameGlobal.UseBulb(add2UsedCount))
                {
                    if (BattleScene.Inst.UseBulbItem())
                    {
                        FlSdk.Inst.ReportUseItemCount(itemId, isByVideo);
                        canUse = true;
                        SoundManager.PlayEffect("slotRot");
                    }
                    else
                    {
                        return;
                    }
                }
                flyPos = btnBulb.GetPos();
                break;
            case ItemId.ItemShuffle:
                if (GameGlobal.UseShuffle(add2UsedCount))
                {
                    FlSdk.Inst.ReportUseItemCount(itemId, isByVideo);
                    canUse = true;
                    SoundManager.PlayEffect("shuffling");
                    BattleScene.Inst.UseShuffleItem();
                }
                flyPos = btnShuffle.GetPos();
                break;
            case ItemId.ItemTurn:
                if (GameGlobal.UseTurn(add2UsedCount))
                {
                    FlSdk.Inst.ReportUseItemCount(itemId, isByVideo);
                    canUse = true;
                    SoundManager.PlayEffect("turn");
                    BattleScene.Inst.UseTurnItem();
                }
                flyPos = btnTurn.GetPos();
                break;
            case ItemId.ItemMagnet:
                // if (GameGlobal.HasMagnet)
                // {
                //     canUse = true;
                //     var useSuccess = BattleScene.Inst.UseMagnetItem();
                //     if (useSuccess)
                //     {
                //         FlSdk.Inst.ReportUseItemCount(itemId, isByVideo);

                //         itemEffect.url = GetCurPackRes("xitieshi");
                //         itemEffect.visible = true;
                //         Timers.inst.Add(1.5f, 1, (obj) =>
                //         {
                //             if (!itemEffect.isDisposed)
                //             {
                //                 itemEffect.visible = false;
                //             }
                //         });
                //         SoundManager.PlayEffect("magnet3");
                //         GameGlobal.UseMagnet(add2UsedCount);
                //     }
                //     else
                //     {
                //         TipMgr.ShowTip("已经找不到匹配的麻将~");
                //     }
                // }
                // flyPos = btnMagnet.GetPos();
                break;
        }

        if (canUse)
        {
            if (add2UsedCount)
            {
                flyPos.y -= 50;
                UIEffectUtil.FlyItem(contentPane, flyPos, itemId, -1);
            }
            UpdateItemCount();
        }
        else
        {
            ShowBuyItemPanel(itemId);
        }
    }

    private void OnSelectItemMajiang(object data)
    {
        var majiangId = (int)data;
        var majiang = BattleScene.Inst.GetMajiang(majiangId);
        if (majiang == null)
            return;

        ShowBuyItemPanel(majiang.Num, majiang);
    }

    private void ShowBuyItemPanel(int itemId, MaJiang majiang = null)
    {
        Pause();
        Create((BuyItemPanel panel) =>
        {
            panel.OnBuySuccess = (BuyType type) =>
            {
                // flyPos.y -= 50;
                // UIEffectUtil.FlyItem(contentPane, flyPos, itemId, 1);
                // UpdateItemCount();
                if (contentPane.isDisposed)
                    return;

                if (majiang != null)
                {
                    majiang.Release();
                    BattleScene.Inst.RemoveMajiang(majiang.Id);
                    UseItem(itemId, type == BuyType.Video, false);
                }
                else
                {
                    UseItem(itemId, type == BuyType.Video, true);
                }
            };
            panel.OnClosed = (type) =>
            {
                if (contentPane.isDisposed)
                    return;
                Resume();
            };
            panel.SetData(itemId, majiang == null);
        });
    }

    internal void SetLeftTime(int time)
    {
        leftTimeBar.SetLeftTime(time, () =>
        {
            new CmdShowResult().Execute(FailReason.TimeOut, true);
        });
    }
}