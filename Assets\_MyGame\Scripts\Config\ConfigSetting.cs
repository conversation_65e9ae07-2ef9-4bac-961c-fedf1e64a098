using System.Collections.Generic;

public class ConfigSetting : ConfigBase
{
    // private static SettingWrapper _setting;
    // public static SettingWrapper Setting
    // {
    //     get
    //     {
    //         _setting ??= new SettingWrapper();
    //         return _setting;
    //     }
    // }
    // public class SettingWrapper
    // {
    /// <summary>每过几关提升难度</summary>
    public static int everyLevelIncreaseDifficulty;
    /// <summary>第一关多少花色</summary>
    public static int startHuaCount;
    /// <summary>每过n关加多少种花色</summary>
    public static int addHuaCount;
    /// <summary>花色上限</summary>
    public static int maxHuaCount;
    /// <summary>第一关多少麻将</summary>
    public static int startMajiangCount;
    /// <summary>每过n关加多少麻将(需要3的倍数)</summary>
    public static int addMajiangCount;
    /// <summary>麻将数量上限</summary>
    public static int maxMajiangCount;
    /// <summary>体验道具数量</summary>
    public static int guideItemCount;
    /// <summary>可复活次数</summary>
    public static int reliveCount;
    /// <summary>清空槽位所需金币</summary>
    public static int clearSlotBarNeedGold;
    /// <summary>加时所需金币</summary>
    public static int addTimeNeedGold;
    /// <summary>加时(秒)</summary>
    public static int addTimeSecond;
    /// <summary>通1关加多少金币到金猪</summary>
    public static int pigAddGold;
    /// <summary>存到多少可领取</summary>
    public static int pigGetGold;
    /// <summary>金猪存储上限</summary>
    public static int pigMaxGold;
    /// <summary>免费体力每次加多少个</summary>
    public static int freeHeartCount;
    /// <summary>最大连击数量</summary>
    public static int maxComboCount;
    /// <summary>每个道具使用上限(10-2表示第10关(包含)内上限都是2个)</summary>
    public static string itemCanUseMaxStr = "";
    /// <summary>胜利结算翻倍倍率条(7个)</summary>
    public static float[] goldMults;

    // public static int GetItemCanUseMax(int level) => ConfigSetting2.GetItemCanUseMax(level);
    // }

    internal override void CacheData(object key, ConfigInfoBase info)
    {
        var infoSetting = info as InfoSetting;
        var id = key.ToString();

        switch (id)
        {
            case "everyLevelIncreaseDifficulty":
                everyLevelIncreaseDifficulty = infoSetting.paramInt;
                break;
            case "startHuaCount":
                startHuaCount = infoSetting.paramInt;
                break;
            case "addHuaCount":
                addHuaCount = infoSetting.paramInt;
                break;
            case "maxHuaCount":
                maxHuaCount = infoSetting.paramInt;
                break;
            case "startMajiangCount":
                startMajiangCount = infoSetting.paramInt;
                break;
            case "addMajiangCount":
                addMajiangCount = infoSetting.paramInt;
                break;
            case "maxMajiangCount":
                maxMajiangCount = infoSetting.paramInt;
                break;
            case "guideItemCount":
                guideItemCount = infoSetting.paramInt;
                break;
            case "reliveCount":
                reliveCount = infoSetting.paramInt;
                break;
            case "clearSlotBarNeedGold":
                clearSlotBarNeedGold = infoSetting.paramInt;
                break;
            case "addTimeNeedGold":
                addTimeNeedGold = infoSetting.paramInt;
                break;
            case "addTimeSecond":
                addTimeSecond = infoSetting.paramInt;
                break;
            case "pigAddGold":
                pigAddGold = infoSetting.paramInt;
                break;
            case "pigGetGold":
                pigGetGold = infoSetting.paramInt;
                break;
            case "pigMaxGold":
                pigMaxGold = infoSetting.paramInt;
                break;
            case "freeHeartCount":
                freeHeartCount = infoSetting.paramInt;
                break;
            case "maxComboCount":
                maxComboCount = infoSetting.paramInt;
                break;
            case "itemCanUseMax":
                itemCanUseMaxStr = infoSetting.paramString;
                break;
            case "goldMult":
                var goldMult = infoSetting.paramString;
                var mults = goldMult.Split(',');
                goldMults = new float[mults.Length];
                for (int i = 0; i < mults.Length; i++)
                {
                    float.TryParse(mults[i], out goldMults[i]);
                }
                break;
        }
    }



    private static Dictionary<int, int> _canUseItemDic;
    public static int GetItemCanUseMax(int level)
    {
        if (_canUseItemDic == null)
        {
            _canUseItemDic = new Dictionary<int, int>();
            var itemAry = itemCanUseMaxStr.Split('|');
            for (int i = 0; i < itemAry.Length; i++)
            {
                var ary = itemAry[i].Split('-');
                if (ary.Length == 2 && int.TryParse(ary[0], out int key) && int.TryParse(ary[1], out int value))
                {
                    _canUseItemDic[key] = value;
                }
            }
        }

        var canUseItemCount = 4;
        foreach (var item in _canUseItemDic)
        {
            if (level <= item.Key)
            {
                canUseItemCount = item.Value;
                break;
            }
        }
        return canUseItemCount;
    }
}